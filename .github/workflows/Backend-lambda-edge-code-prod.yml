name: Deploy <PERSON><PERSON>@Edge and Update CloudFront - Prod

on:
  push:
    branches:
      - main
    paths:
      - terraform/lambda_edge_function/index.js
      - .github/workflows/Backend-lambda-edge-code-prod.yml
  workflow_dispatch:  # Allow manual trigger from GitHub UI

jobs:
  deploy_lambda_edge:
    runs-on: ubuntu-latest

    env:
      FUNCTION_NAME: mtl-edge-redirect-prod # Lambda@Edge Function Name

    steps:

    # Determine display name for the author
    - name: Check if author needs to be replaced
      id: check_author
      run: |
        if [ "${{ github.actor }}" = "24072012" ]; then
          echo "author=Strapi" >> $GITHUB_OUTPUT
        else
          echo "author=${{ github.actor }}" >> $GITHUB_OUTPUT
        fi
      shell: bash

    # Notify Slack that deployment has started
    - name: Notify Deployment startup to Slack
      uses: slackapi/slack-github-action@v1.23.0
      with:
        channel-id: 'C05EU6F4EG6'
        payload: |
          {
            "text":  "_Event: Deployment Started_ :arrows_counterclockwise: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda@Edge \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "_Event: Deployment Started_ :arrows_counterclockwise: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda@Edge \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            ]
          }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

    - name: Checkout code
      uses: actions/checkout@v4

    # Configure AWS CLI credentials for us-east-1 region
    - name: Set up AWS credentials for us-east-1
      uses: aws-actions/configure-aws-credentials@v3
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    # Zip the Lambda@Edge function code
    - name: Zip the Lambda@Edge Function
      run: |
        zip lambda_edge_code.zip terraform/lambda_edge_function/index.js -j
    
    # Deploy and publish new Lambda@Edge version, capture published version number
    - name: Deploy and Publish Lambda@Edge Function
      id: publish_lambda 
      run: |        
        response=$(aws lambda update-function-code \
          --function-name $FUNCTION_NAME \
          --zip-file fileb://lambda_edge_code.zip \
          --publish)
        
        version=$(echo "$response" | jq -r '.Version')
        echo "Lambda@Edge function published with version: $version"
        echo "version=$version" >> $GITHUB_OUTPUT

    # Wait for Lambda version to become Active
    - name: Wait for Lambda to become Active
      run: |
        VERSION=${{ steps.publish_lambda.outputs.version }}
        echo "Waiting for Lambda version $VERSION to become Active..."

        while true; do
          state=$(aws lambda get-function \ 
            --function-name $FUNCTION_NAME \
            --qualifier $VERSION \
            --query 'Configuration.State' \
            --output text)

          echo "Current state: $state"
          if [ "$state" = "Active" ]; then
            echo "Lambda function is now Active."
            break
          fi
          echo "Waiting 5 seconds..."
          sleep 5
        done

    # Get current CloudFront distribution config
    - name: Fetch current CloudFront config
      run: |
        aws cloudfront get-distribution-config --id ${{ secrets.cloudfront_id_production }} > dist_config.json
        jq '.DistributionConfig' dist_config.json > config.json
        echo "ETAG=$(jq -r '.ETag' dist_config.json)" >> $GITHUB_ENV

    # Update Lambda@Edge version in CloudFront config
    - name: Update Lambda@Edge version in config
      run: |
        # Fully-qualified new Lambda ARN
        NEW_ARN="arn:aws:lambda:us-east-1:${{ secrets.AWS_ACCOUNT_ID }}:function:${{ env.FUNCTION_NAME }}:${{ steps.publish_lambda.outputs.version }}"
        echo "Using new Lambda ARN: $NEW_ARN"

        # Replace the first LambdaFunctionAssociation (viewer-request) ARN
        jq --arg arn "$NEW_ARN" '
          .DefaultCacheBehavior.LambdaFunctionAssociations.Items[0].LambdaFunctionARN = $arn
        ' config.json > updated_config.json

    # Deploy updated CloudFront distribution with new Lambda version
    - name: Deploy updated CloudFront distribution
      run: |
        aws cloudfront update-distribution \
          --id ${{ secrets.cloudfront_id_production }} \
          --distribution-config file://updated_config.json \
          --if-match $ETAG

    # Invalidate CloudFront cache
    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation \
          --distribution-id ${{ secrets.cloudfront_id_production }} \
          --paths "/*"

    # Notify Slack when the deployment completes successfully
    - name: Notify Deployment completion to Slack
      uses: slackapi/slack-github-action@v1.23.0
      with:
        channel-id: 'C05EU6F4EG6'
        payload: |
          {
            "text":  "_Event: Deployment Completed_ :rocket: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda@Edge \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "_Event: Deployment Completed_ :rocket: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda@Edge \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            ]
          }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

    # Notify Slack if any previous step in the workflow fails
    - name: Notify Deployment failure to Slack
      if: failure()
      uses: slackapi/slack-github-action@v1.23.0
      with:
        channel-id: 'C05EU6F4EG6'
        payload: |
          {
            "text":  "_Event: Deployment Failed_ :rotating_light: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda@Edge \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "_Event: Deployment Failed_ :rotating_light: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda@Edge \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            ]
          }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}